/**
 * 混合空间状态切片
 */
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { blendSpaceService, BlendSpaceData } from '../../services/blendSpaceService';

// 混合空间状态
interface BlendSpaceState {
  blendSpaces: BlendSpaceData[];
  blendSpace: BlendSpaceData | null;
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: BlendSpaceState = {
  blendSpaces: [],
  blendSpace: null,
  loading: false,
  error: null
};

// 异步操作：加载所有混合空间
export const loadBlendSpaces = createAsyncThunk(
  'blendSpace/loadBlendSpaces',
  async (entityId: string) => {
    return await blendSpaceService.getBlendSpaces(entityId);
  }
);

// 异步操作：加载1D混合空间
export const loadBlendSpace1D = createAsyncThunk(
  'blendSpace/loadBlendSpace1D',
  async ({ entityId, blendSpaceId }: { entityId: string; blendSpaceId: string }) => {
    return await blendSpaceService.getBlendSpace(entityId, blendSpaceId);
  }
);

// 异步操作：加载2D混合空间
export const loadBlendSpace2D = createAsyncThunk(
  'blendSpace/loadBlendSpace2D',
  async ({ entityId, blendSpaceId }: { entityId: string; blendSpaceId: string }) => {
    return await blendSpaceService.getBlendSpace(entityId, blendSpaceId);
  }
);

// 异步操作：更新1D混合空间
export const updateBlendSpace1D = createAsyncThunk(
  'blendSpace/updateBlendSpace1D',
  async ({ entityId, blendSpaceId, data }: { entityId: string; blendSpaceId: string; data: any }) => {
    return await blendSpaceService.updateBlendSpace(entityId, blendSpaceId, data);
  }
);

// 异步操作：更新2D混合空间
export const updateBlendSpace2D = createAsyncThunk(
  'blendSpace/updateBlendSpace2D',
  async ({ entityId, blendSpaceId, data }: { entityId: string; blendSpaceId: string; data: any }) => {
    return await blendSpaceService.updateBlendSpace(entityId, blendSpaceId, data);
  }
);

// 异步操作：添加1D混合空间节点
export const addBlendSpace1DNode = createAsyncThunk(
  'blendSpace/addBlendSpace1DNode',
  async ({ entityId, blendSpaceId, data }: { entityId: string; blendSpaceId: string; data: any }) => {
    return await blendSpaceService.addBlendSpaceNode(entityId, blendSpaceId, data);
  }
);

// 异步操作：添加2D混合空间节点
export const addBlendSpace2DNode = createAsyncThunk(
  'blendSpace/addBlendSpace2DNode',
  async ({ entityId, blendSpaceId, data }: { entityId: string; blendSpaceId: string; data: any }) => {
    return await blendSpaceService.addBlendSpaceNode(entityId, blendSpaceId, data);
  }
);

// 异步操作：更新1D混合空间节点
export const updateBlendSpace1DNode = createAsyncThunk(
  'blendSpace/updateBlendSpace1DNode',
  async ({ entityId, blendSpaceId, nodeId, data }: { entityId: string; blendSpaceId: string; nodeId: string; data: any }) => {
    return await blendSpaceService.updateBlendSpaceNode(entityId, blendSpaceId, nodeId, data);
  }
);

// 异步操作：更新2D混合空间节点
export const updateBlendSpace2DNode = createAsyncThunk(
  'blendSpace/updateBlendSpace2DNode',
  async ({ entityId, blendSpaceId, nodeId, data }: { entityId: string; blendSpaceId: string; nodeId: string; data: any }) => {
    return await blendSpaceService.updateBlendSpaceNode(entityId, blendSpaceId, nodeId, data);
  }
);

// 异步操作：删除混合空间节点
export const removeBlendSpace1DNode = createAsyncThunk(
  'blendSpace/removeBlendSpace1DNode',
  async ({ entityId, blendSpaceId, nodeId }: { entityId: string; blendSpaceId: string; nodeId: string }) => {
    const success = await blendSpaceService.removeBlendSpaceNode(entityId, blendSpaceId, nodeId);
    return { success, nodeId };
  }
);

// 异步操作：删除混合空间节点
export const removeBlendSpace2DNode = createAsyncThunk(
  'blendSpace/removeBlendSpace2DNode',
  async ({ entityId, blendSpaceId, nodeId }: { entityId: string; blendSpaceId: string; nodeId: string }) => {
    const success = await blendSpaceService.removeBlendSpaceNode(entityId, blendSpaceId, nodeId);
    return { success, nodeId };
  }
);

// 异步操作：设置1D混合空间位置
export const setBlendSpace1DPosition = createAsyncThunk(
  'blendSpace/setBlendSpace1DPosition',
  async ({ entityId, blendSpaceId, position }: { entityId: string; blendSpaceId: string; position: number }) => {
    return await blendSpaceService.setBlendSpacePosition(entityId, blendSpaceId, position);
  }
);

// 异步操作：设置2D混合空间位置
export const setBlendSpace2DPosition = createAsyncThunk(
  'blendSpace/setBlendSpace2DPosition',
  async ({ entityId, blendSpaceId, position }: { entityId: string; blendSpaceId: string; position: { x: number; y: number } }) => {
    return await blendSpaceService.setBlendSpacePosition(entityId, blendSpaceId, position);
  }
);

// 混合空间切片
const blendSpaceSlice = createSlice({
  name: 'blendSpace',
  initialState,
  reducers: {
    // 清空混合空间
    clearBlendSpaces: (state) => {
      state.blendSpaces = [];
      state.blendSpace = null;
      state.loading = false;
      state.error = null;
    },

    // 清空当前混合空间
    clearBlendSpace: (state) => {
      state.blendSpace = null;
    }
  },
  extraReducers: (builder) => {
    // 加载所有混合空间
    builder.addCase(loadBlendSpaces.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(loadBlendSpaces.fulfilled, (state, action) => {
      state.loading = false;
      state.blendSpaces = action.payload;
    });
    builder.addCase(loadBlendSpaces.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '加载混合空间失败';
    });

    // 加载1D混合空间
    builder.addCase(loadBlendSpace1D.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(loadBlendSpace1D.fulfilled, (state, action) => {
      state.loading = false;
      state.blendSpace = action.payload;
    });
    builder.addCase(loadBlendSpace1D.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '加载1D混合空间失败';
    });

    // 加载2D混合空间
    builder.addCase(loadBlendSpace2D.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(loadBlendSpace2D.fulfilled, (state, action) => {
      state.loading = false;
      state.blendSpace = action.payload;
    });
    builder.addCase(loadBlendSpace2D.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '加载2D混合空间失败';
    });

    // 更新1D混合空间
    builder.addCase(updateBlendSpace1D.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateBlendSpace1D.fulfilled, (state, action) => {
      state.loading = false;
      state.blendSpace = action.payload;

      // 更新列表中的混合空间
      if (action.payload) {
        const index = state.blendSpaces.findIndex(bs => bs.id === action.payload!.id);
        if (index !== -1) {
          state.blendSpaces[index] = action.payload;
        }
      }
    });
    builder.addCase(updateBlendSpace1D.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '更新1D混合空间失败';
    });

    // 更新2D混合空间
    builder.addCase(updateBlendSpace2D.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateBlendSpace2D.fulfilled, (state, action) => {
      state.loading = false;
      state.blendSpace = action.payload;

      // 更新列表中的混合空间
      if (action.payload) {
        const index = state.blendSpaces.findIndex(bs => bs.id === action.payload!.id);
        if (index !== -1) {
          state.blendSpaces[index] = action.payload;
        }
      }
    });
    builder.addCase(updateBlendSpace2D.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '更新2D混合空间失败';
    });

    // 添加1D混合空间节点
    builder.addCase(addBlendSpace1DNode.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(addBlendSpace1DNode.fulfilled, (state, action) => {
      state.loading = false;

      // 添加节点到当前混合空间
      if (state.blendSpace && action.payload) {
        state.blendSpace.nodes.push(action.payload);
        state.blendSpace.nodeCount = state.blendSpace.nodes.length;
      }
    });
    builder.addCase(addBlendSpace1DNode.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '添加1D混合空间节点失败';
    });

    // 添加2D混合空间节点
    builder.addCase(addBlendSpace2DNode.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(addBlendSpace2DNode.fulfilled, (state, action) => {
      state.loading = false;

      // 添加节点到当前混合空间
      if (state.blendSpace && action.payload) {
        state.blendSpace.nodes.push(action.payload);
        state.blendSpace.nodeCount = state.blendSpace.nodes.length;
      }
    });
    builder.addCase(addBlendSpace2DNode.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '添加2D混合空间节点失败';
    });

    // 更新1D混合空间节点
    builder.addCase(updateBlendSpace1DNode.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateBlendSpace1DNode.fulfilled, (state, action) => {
      state.loading = false;

      // 更新当前混合空间中的节点
      if (state.blendSpace && action.payload) {
        const index = state.blendSpace.nodes.findIndex(node => node.id === action.payload!.id);
        if (index !== -1) {
          state.blendSpace.nodes[index] = action.payload;
        }
      }
    });
    builder.addCase(updateBlendSpace1DNode.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '更新1D混合空间节点失败';
    });

    // 更新2D混合空间节点
    builder.addCase(updateBlendSpace2DNode.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateBlendSpace2DNode.fulfilled, (state, action) => {
      state.loading = false;

      // 更新当前混合空间中的节点
      if (state.blendSpace && action.payload) {
        const index = state.blendSpace.nodes.findIndex(node => node.id === action.payload!.id);
        if (index !== -1) {
          state.blendSpace.nodes[index] = action.payload;
        }
      }
    });
    builder.addCase(updateBlendSpace2DNode.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '更新2D混合空间节点失败';
    });

    // 删除1D混合空间节点
    builder.addCase(removeBlendSpace1DNode.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(removeBlendSpace1DNode.fulfilled, (state, action) => {
      state.loading = false;

      // 从当前混合空间中删除节点
      if (state.blendSpace && action.payload.success) {
        state.blendSpace.nodes = state.blendSpace.nodes.filter(node => node.id !== action.payload.nodeId);
        state.blendSpace.nodeCount = state.blendSpace.nodes.length;
      }
    });
    builder.addCase(removeBlendSpace1DNode.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '删除1D混合空间节点失败';
    });

    // 删除2D混合空间节点
    builder.addCase(removeBlendSpace2DNode.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(removeBlendSpace2DNode.fulfilled, (state, action) => {
      state.loading = false;

      // 从当前混合空间中删除节点
      if (state.blendSpace && action.payload.success) {
        state.blendSpace.nodes = state.blendSpace.nodes.filter(node => node.id !== action.payload.nodeId);
        state.blendSpace.nodeCount = state.blendSpace.nodes.length;
      }
    });
    builder.addCase(removeBlendSpace2DNode.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '删除2D混合空间节点失败';
    });

    // 设置1D混合空间位置
    builder.addCase(setBlendSpace1DPosition.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(setBlendSpace1DPosition.fulfilled, (state, action) => {
      state.loading = false;

      // 更新当前混合空间的位置
      if (state.blendSpace && action.payload) {
        state.blendSpace = action.payload;
      }
    });
    builder.addCase(setBlendSpace1DPosition.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '设置1D混合空间位置失败';
    });

    // 设置2D混合空间位置
    builder.addCase(setBlendSpace2DPosition.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(setBlendSpace2DPosition.fulfilled, (state, action) => {
      state.loading = false;

      // 更新当前混合空间的位置
      if (state.blendSpace && action.payload) {
        state.blendSpace = action.payload;
      }
    });
    builder.addCase(setBlendSpace2DPosition.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || '设置2D混合空间位置失败';
    });
  }
});

// 导出操作
export const { clearBlendSpaces, clearBlendSpace } = blendSpaceSlice.actions;

// 导出reducer
export default blendSpaceSlice.reducer;
