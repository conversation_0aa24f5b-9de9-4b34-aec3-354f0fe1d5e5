/**
 * 冲突可视化服务
 * 提供冲突差异高亮显示和冲突历史记录功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { store } from '../store';
import {
  addConflictVisualization,
  removeConflictVisualization,
  clearConflictVisualizations,
  addConflictHistory,
  clearConflictHistory
} from '../store/collaboration/conflictVisualizationSlice';
import { Conflict, ConflictStatus } from './ConflictResolutionService';

/**
 * 冲突可视化类型枚举
 */
export enum VisualizationType {
  ENTITY_HIGHLIGHT = 'entity_highlight',
  COMPONENT_HIGHLIGHT = 'component_highlight',
  PROPERTY_HIGHLIGHT = 'property_highlight',
  SCENE_HIGHLIGHT = 'scene_highlight'
}

/**
 * 冲突可视化接口
 */
export interface ConflictVisualization {
  id: string;
  conflictId: string;
  type: VisualizationType;
  entityId?: string;
  componentId?: string;
  propertyPath?: string[];
  color: string;
  createdAt: number;
}

/**
 * 冲突历史记录接口
 */
export interface ConflictHistoryEntry {
  id: string;
  conflictId: string;
  conflict: Conflict;
  createdAt: number;
  resolvedAt?: number;
}

/**
 * 冲突可视化服务类
 */
class ConflictVisualizationService extends EventEmitter {
  private enabled: boolean = false;
  private visualizations: Map<string, ConflictVisualization> = new Map();
  private history: Map<string, ConflictHistoryEntry> = new Map();
  private maxHistoryEntries: number = 100;
  
  // 高亮颜色配置
  private colors = {
    local: '#4caf50',  // 绿色
    remote: '#2196f3', // 蓝色
    conflict: '#f44336' // 红色
  };

  constructor() {
    super();
  }

  /**
   * 设置是否启用可视化服务
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    
    if (!enabled) {
      this.clearAllVisualizations();
    }
  }

  /**
   * 设置最大历史记录数量
   * @param max 最大数量
   */
  public setMaxHistoryEntries(max: number): void {
    this.maxHistoryEntries = max;
    this.trimHistory();
  }

  /**
   * 为冲突创建可视化
   * @param conflict 冲突对象
   */
  public createVisualizationForConflict(conflict: Conflict): void {
    if (!this.enabled) return;

    // 清除该冲突的现有可视化
    this.removeVisualizationForConflict(conflict.id);

    // 根据冲突类型创建不同的可视化
    switch (conflict.type) {
      case 'entity_conflict':
        this.createEntityVisualization(conflict);
        break;
      case 'component_conflict':
        this.createComponentVisualization(conflict);
        break;
      case 'property_conflict':
        this.createPropertyVisualization(conflict);
        break;
      case 'scene_conflict':
        this.createSceneVisualization(conflict);
        break;
    }

    // 添加到历史记录
    this.addToHistory(conflict);
  }

  /**
   * 移除冲突的可视化
   * @param conflictId 冲突ID
   */
  public removeVisualizationForConflict(conflictId: string): void {
    // 查找并移除该冲突的所有可视化
    const toRemove: string[] = [];
    
    this.visualizations.forEach((vis, id) => {
      if (vis.conflictId === conflictId) {
        toRemove.push(id);
      }
    });
    
    toRemove.forEach(id => {
      this.visualizations.delete(id);
      store.dispatch(removeConflictVisualization(id));
    });
  }

  /**
   * 清除所有可视化
   */
  public clearAllVisualizations(): void {
    this.visualizations.clear();
    store.dispatch(clearConflictVisualizations());
  }

  /**
   * 获取冲突历史记录
   * @returns 历史记录数组
   */
  public getHistory(): ConflictHistoryEntry[] {
    return Array.from(this.history.values())
      .sort((a, b) => b.createdAt - a.createdAt);
  }

  /**
   * 清除历史记录
   */
  public clearHistory(): void {
    this.history.clear();
    store.dispatch(clearConflictHistory());
  }

  /**
   * 创建实体可视化
   * @private
   */
  private createEntityVisualization(conflict: Conflict): void {
    const entityId = conflict.localOperation.data?.entityId || 
                     conflict.remoteOperation.data?.entityId;
    
    if (!entityId) return;
    
    const visualization: ConflictVisualization = {
      id: this.generateId(),
      conflictId: conflict.id,
      type: VisualizationType.ENTITY_HIGHLIGHT,
      entityId,
      color: this.colors.conflict,
      createdAt: Date.now()
    };
    
    this.visualizations.set(visualization.id, visualization);
    store.dispatch(addConflictVisualization(visualization));
    
    this.emit('visualizationCreated', visualization);
  }

  /**
   * 创建组件可视化
   * @private
   */
  private createComponentVisualization(conflict: Conflict): void {
    const entityId = conflict.localOperation.data?.entityId || 
                     conflict.remoteOperation.data?.entityId;
    const componentId = conflict.localOperation.data?.componentId || 
                        conflict.remoteOperation.data?.componentId;
    
    if (!entityId || !componentId) return;
    
    const visualization: ConflictVisualization = {
      id: this.generateId(),
      conflictId: conflict.id,
      type: VisualizationType.COMPONENT_HIGHLIGHT,
      entityId,
      componentId,
      color: this.colors.conflict,
      createdAt: Date.now()
    };
    
    this.visualizations.set(visualization.id, visualization);
    store.dispatch(addConflictVisualization(visualization));
    
    this.emit('visualizationCreated', visualization);
  }

  /**
   * 创建属性可视化
   * @private
   */
  private createPropertyVisualization(conflict: Conflict): void {
    const entityId = conflict.localOperation.data?.entityId || 
                     conflict.remoteOperation.data?.entityId;
    const componentId = conflict.localOperation.data?.componentId || 
                        conflict.remoteOperation.data?.componentId;
    const propertyPath = conflict.localOperation.data?.path || 
                         conflict.remoteOperation.data?.path;
    
    if (!entityId || !propertyPath) return;
    
    const visualization: ConflictVisualization = {
      id: this.generateId(),
      conflictId: conflict.id,
      type: VisualizationType.PROPERTY_HIGHLIGHT,
      entityId,
      componentId,
      propertyPath,
      color: this.colors.conflict,
      createdAt: Date.now()
    };
    
    this.visualizations.set(visualization.id, visualization);
    store.dispatch(addConflictVisualization(visualization));
    
    this.emit('visualizationCreated', visualization);
  }

  /**
   * 创建场景可视化
   * @private
   */
  private createSceneVisualization(conflict: Conflict): void {
    const visualization: ConflictVisualization = {
      id: this.generateId(),
      conflictId: conflict.id,
      type: VisualizationType.SCENE_HIGHLIGHT,
      color: this.colors.conflict,
      createdAt: Date.now()
    };
    
    this.visualizations.set(visualization.id, visualization);
    store.dispatch(addConflictVisualization(visualization));
    
    this.emit('visualizationCreated', visualization);
  }

  /**
   * 添加到历史记录
   * @private
   */
  private addToHistory(conflict: Conflict): void {
    const entry: ConflictHistoryEntry = {
      id: this.generateId(),
      conflictId: conflict.id,
      conflict: { ...conflict },
      createdAt: Date.now()
    };
    
    if (conflict.status === ConflictStatus.RESOLVED) {
      entry.resolvedAt = conflict.resolvedAt || Date.now();
    }
    
    this.history.set(entry.id, entry);
    store.dispatch(addConflictHistory(entry));
    
    this.trimHistory();
  }

  /**
   * 裁剪历史记录
   * @private
   */
  private trimHistory(): void {
    if (this.history.size <= this.maxHistoryEntries) return;
    
    // 按创建时间排序
    const entries = Array.from(this.history.values())
      .sort((a, b) => a.createdAt - b.createdAt);
    
    // 删除最旧的条目
    const toRemove = entries.slice(0, this.history.size - this.maxHistoryEntries);
    
    toRemove.forEach(entry => {
      this.history.delete(entry.id);
    });
  }

  /**
   * 生成唯一ID
   * @private
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
  }
}

// 导出单例
export const conflictVisualizationService = new ConflictVisualizationService();
