/**
 * 面部动画预设面板测试
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { FacialAnimationPresetPanel } from '../FacialAnimationPresetPanel';

// Mock i18n
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, options?: any) => {
      const translations: Record<string, string> = {
        'editor.animation.presets': '预设',
        'editor.animation.templates': '模板',
        'editor.animation.totalPresets': '预设总数',
        'editor.animation.totalTemplates': '模板总数',
        'editor.animation.favoritePresets': '收藏预设',
        'editor.animation.recentlyUsed': '最近使用',
        'editor.animation.searchPresets': '搜索预设',
        'editor.animation.filterByType': '按类型筛选',
        'editor.animation.filterByCulture': '按文化筛选',
        'editor.animation.allTypes': '所有类型',
        'editor.animation.allCultures': '所有文化',
        'editor.animation.favorites': '收藏',
        'editor.animation.history': '历史',
        'editor.animation.batchMode': '批量模式',
        'editor.animation.refresh': '刷新',
        'editor.animation.settings': '设置',
        'editor.animation.selected': '已选择',
        'editor.animation.settingsComingSoon': '设置功能即将推出',
        'common.close': '关闭'
      };
      
      if (options && typeof options === 'object') {
        let result = translations[key] || key;
        Object.keys(options).forEach(optionKey => {
          result = result.replace(`{{${optionKey}}}`, options[optionKey]);
        });
        return result;
      }
      
      return translations[key] || key;
    }
  })
}));

// Mock 服务
jest.mock('../../services/FacialAnimationPresetService', () => ({
  facialAnimationPresetService: {
    getAllPresets: jest.fn().mockResolvedValue([
      {
        id: 'preset1',
        name: '测试预设1',
        type: 'STANDARD',
        tags: ['favorite']
      },
      {
        id: 'preset2',
        name: '测试预设2',
        type: 'CULTURAL',
        tags: []
      }
    ])
  }
}));

// Mock 组件
jest.mock('../../components/FacialAnimationEditor/FacialAnimationPresetManager', () => ({
  FacialAnimationPresetManager: ({ onPresetApply, onPresetImport, onPresetExport }: any) => (
    <div data-testid="preset-manager">
      <button onClick={() => onPresetApply?.({ id: 'test', name: '测试预设' })}>
        应用预设
      </button>
      <button onClick={() => onPresetImport?.([{ id: 'import1', name: '导入预设' }])}>
        导入预设
      </button>
      <button onClick={() => onPresetExport?.([{ id: 'export1', name: '导出预设' }])}>
        导出预设
      </button>
    </div>
  ),
  FacialAnimationPresetType: {
    STANDARD: 'STANDARD',
    CULTURAL: 'CULTURAL',
    EMOTION_COMBO: 'EMOTION_COMBO',
    ANIMATION_SEQUENCE: 'ANIMATION_SEQUENCE',
    CUSTOM: 'CUSTOM'
  }
}));

jest.mock('../../components/FacialAnimationEditor/FacialAnimationTemplateManager', () => ({
  FacialAnimationTemplateManager: ({ onTemplateApply, onTemplateImport, onTemplateExport }: any) => (
    <div data-testid="template-manager">
      <button onClick={() => onTemplateApply?.({ id: 'template1', name: '测试模板' }, {})}>
        应用模板
      </button>
      <button onClick={() => onTemplateImport?.([{ id: 'import1', name: '导入模板' }])}>
        导入模板
      </button>
      <button onClick={() => onTemplateExport?.([{ id: 'export1', name: '导出模板' }])}>
        导出模板
      </button>
    </div>
  )
}));

// Mock antd message
const mockMessage = {
  success: jest.fn(),
  error: jest.fn(),
  warning: jest.fn(),
  info: jest.fn()
};

jest.mock('antd', () => {
  const antd = jest.requireActual('antd');
  return {
    ...antd,
    message: mockMessage
  };
});

describe('FacialAnimationPresetPanel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // 清除本地存储
    localStorage.clear();
  });

  it('应该正确渲染基本组件', () => {
    render(<FacialAnimationPresetPanel />);
    
    expect(screen.getByText('预设')).toBeInTheDocument();
    expect(screen.getByText('模板')).toBeInTheDocument();
  });

  it('应该显示统计信息', async () => {
    render(<FacialAnimationPresetPanel showStatistics={true} />);
    
    await waitFor(() => {
      expect(screen.getByText('预设总数')).toBeInTheDocument();
      expect(screen.getByText('模板总数')).toBeInTheDocument();
      expect(screen.getByText('收藏预设')).toBeInTheDocument();
      expect(screen.getByText('最近使用')).toBeInTheDocument();
    });
  });

  it('应该显示快速操作栏', () => {
    render(<FacialAnimationPresetPanel showQuickActions={true} />);
    
    expect(screen.getByPlaceholderText('搜索预设')).toBeInTheDocument();
    expect(screen.getByText('收藏')).toBeInTheDocument();
    expect(screen.getByText('历史')).toBeInTheDocument();
    expect(screen.getByText('批量模式')).toBeInTheDocument();
    expect(screen.getByText('刷新')).toBeInTheDocument();
  });

  it('应该处理预设应用', async () => {
    const onPresetApply = jest.fn();
    render(<FacialAnimationPresetPanel onPresetApply={onPresetApply} />);
    
    const applyButton = screen.getByText('应用预设');
    fireEvent.click(applyButton);
    
    expect(onPresetApply).toHaveBeenCalledWith({ id: 'test', name: '测试预设' });
  });

  it('应该处理模板应用', async () => {
    const onTemplateApply = jest.fn();
    render(<FacialAnimationPresetPanel onTemplateApply={onTemplateApply} />);
    
    // 切换到模板标签页
    const templateTab = screen.getByText('模板');
    fireEvent.click(templateTab);
    
    const applyButton = screen.getByText('应用模板');
    fireEvent.click(applyButton);
    
    expect(onTemplateApply).toHaveBeenCalledWith({ id: 'template1', name: '测试模板' }, {});
  });

  it('应该处理批量操作', () => {
    const onBatchOperation = jest.fn();
    render(<FacialAnimationPresetPanel onBatchOperation={onBatchOperation} enableBatchOperations={true} />);
    
    // 启用批量模式
    const batchModeButton = screen.getByText('批量模式');
    fireEvent.click(batchModeButton);
    
    // 验证批量模式已启用
    expect(batchModeButton).toHaveClass('ant-btn-primary');
  });

  it('应该处理搜索功能', () => {
    render(<FacialAnimationPresetPanel showQuickActions={true} />);
    
    const searchInput = screen.getByPlaceholderText('搜索预设');
    fireEvent.change(searchInput, { target: { value: '测试搜索' } });
    
    expect(searchInput).toHaveValue('测试搜索');
  });

  it('应该处理历史记录', () => {
    // 设置一些历史记录
    const historyData = [
      {
        id: 'history1',
        presetId: 'preset1',
        presetName: '历史预设1',
        appliedAt: new Date(),
        entityId: 'entity1'
      }
    ];
    localStorage.setItem('facial-animation-preset-history', JSON.stringify(historyData));
    
    render(<FacialAnimationPresetPanel showHistory={true} />);
    
    const historyButton = screen.getByText('历史');
    fireEvent.click(historyButton);
    
    // 验证历史模态框打开
    expect(screen.getByText('历史预设1')).toBeInTheDocument();
  });

  it('应该正确处理设置按钮', () => {
    render(<FacialAnimationPresetPanel />);
    
    const settingsButton = screen.getByRole('button', { name: /settings/i });
    fireEvent.click(settingsButton);
    
    expect(mockMessage.info).toHaveBeenCalledWith('设置功能即将推出');
  });

  it('应该支持禁用功能', () => {
    render(
      <FacialAnimationPresetPanel
        showStatistics={false}
        showQuickActions={false}
        showFavorites={false}
        showHistory={false}
        enableBatchOperations={false}
      />
    );
    
    // 验证功能被禁用
    expect(screen.queryByText('预设总数')).not.toBeInTheDocument();
    expect(screen.queryByPlaceholderText('搜索预设')).not.toBeInTheDocument();
    expect(screen.queryByText('收藏')).not.toBeInTheDocument();
    expect(screen.queryByText('历史')).not.toBeInTheDocument();
    expect(screen.queryByText('批量模式')).not.toBeInTheDocument();
  });
});
