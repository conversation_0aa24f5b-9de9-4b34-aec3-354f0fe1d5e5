/**
 * 工具栏组件
 */
import React from 'react';
import { Dropdown, Button, Space, Divider, Tooltip } from 'antd';
import {
  PlusOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  ExportOutlined,
  ImportOutlined,
  SettingOutlined,
  UndoOutlined,
  RedoOutlined,
  Sc<PERSON>orOutlined,
  <PERSON>pyOutlined,
  SnippetsOutlined,
  DeleteOutlined,
  ArrowsAltOutlined,
  RotateRightOutlined,
  ColumnWidthOutlined,
  BorderOutlined,
  AppstoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SelectOutlined,
  EyeOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setTransformSpace,
  setSnapMode,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace,
  SnapMode,
  undo,
  redo} from '../../store/editor/editorSlice';
import { openDialog, DialogType } from '../../store/ui/uiSlice';

const Toolbar: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  
  const {
    showGrid,
    showAxes,
    isPlaying} = useAppSelector((state) => state.editor);
  
  // 文件菜单
  const fileMenuItems = [
    {
      key: 'new',
      icon: <PlusOutlined />,
      label: t('editor.newProject'),
      onClick: () => dispatch(openDialog({ type: DialogType.NEW_PROJECT, title: t('editor.newProject') }))
    },
    {
      key: 'open',
      icon: <FolderOpenOutlined />,
      label: t('editor.openProject'),
      onClick: () => dispatch(openDialog({ type: DialogType.OPEN_PROJECT, title: t('editor.openProject') }))
    },
    { type: 'divider' as const },
    {
      key: 'save',
      icon: <SaveOutlined />,
      label: t('editor.saveProject')
    },
    {
      key: 'saveAs',
      icon: <SaveOutlined />,
      label: t('editor.saveProjectAs'),
      onClick: () => dispatch(openDialog({ type: DialogType.SAVE_PROJECT_AS, title: t('editor.saveProjectAs') }))
    },
    { type: 'divider' as const },
    {
      key: 'import',
      icon: <ImportOutlined />,
      label: t('editor.importAsset'),
      onClick: () => dispatch(openDialog({ type: DialogType.IMPORT_ASSET, title: t('editor.importAsset') }))
    },
    {
      key: 'export',
      icon: <ExportOutlined />,
      label: t('editor.exportScene'),
      onClick: () => dispatch(openDialog({ type: DialogType.EXPORT_SCENE, title: t('editor.exportScene') }))
    },
    { type: 'divider' as const },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('editor.projectSettings'),
      onClick: () => dispatch(openDialog({ type: DialogType.PROJECT_SETTINGS, title: t('editor.projectSettings') }))
    }
  ];
  
  // 编辑菜单
  const editMenuItems = [
    {
      key: 'undo',
      icon: <UndoOutlined />,
      label: t('editor.undo'),
      onClick: () => dispatch(undo())
    },
    {
      key: 'redo',
      icon: <RedoOutlined />,
      label: t('editor.redo'),
      onClick: () => dispatch(redo())
    },
    { type: 'divider' as const },
    {
      key: 'cut',
      icon: <ScissorOutlined />,
      label: t('editor.cut')
    },
    {
      key: 'copy',
      icon: <CopyOutlined />,
      label: t('editor.copy')
    },
    {
      key: 'paste',
      icon: <SnippetsOutlined />,
      label: t('editor.paste')
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: t('editor.delete')
    }
  ];
  
  // 视图菜单
  const viewMenuItems = [
    {
      key: 'grid',
      icon: <BorderOutlined />,
      label: showGrid ? t('editor.hideGrid') : t('editor.showGrid'),
      onClick: () => dispatch(setShowGrid(!showGrid))
    },
    {
      key: 'axes',
      icon: <AppstoreOutlined />,
      label: showAxes ? t('editor.hideAxes') : t('editor.showAxes'),
      onClick: () => dispatch(setShowAxes(!showAxes))
    },
    { type: 'divider' as const },
    {
      key: 'sceneView',
      icon: <EyeOutlined />,
      label: t('editor.sceneView')
    },
    {
      key: 'gameView',
      icon: <PlayCircleOutlined />,
      label: t('editor.gameView')
    }
  ];
  
  // 工具菜单
  const toolsMenuItems = [
    {
      key: 'select',
      icon: <SelectOutlined />,
      label: t('editor.selectTool')
    },
    {
      key: 'translate',
      icon: <ArrowsAltOutlined />,
      label: t('editor.translateTool'),
      onClick: () => dispatch(setTransformMode(TransformMode.TRANSLATE))
    },
    {
      key: 'rotate',
      icon: <RotateRightOutlined />,
      label: t('editor.rotateTool'),
      onClick: () => dispatch(setTransformMode(TransformMode.ROTATE))
    },
    {
      key: 'scale',
      icon: <ColumnWidthOutlined />,
      label: t('editor.scaleTool'),
      onClick: () => dispatch(setTransformMode(TransformMode.SCALE))
    },
    { type: 'divider' as const },
    {
      key: 'transformSpace',
      label: t('editor.transformSpace'),
      children: [
        {
          key: 'local',
          label: t('editor.localSpace'),
          onClick: () => dispatch(setTransformSpace(TransformSpace.LOCAL))
        },
        {
          key: 'world',
          label: t('editor.worldSpace'),
          onClick: () => dispatch(setTransformSpace(TransformSpace.WORLD))
        }
      ]
    },
    {
      key: 'snapMode',
      label: t('editor.snapMode'),
      children: [
        {
          key: 'disabled',
          label: t('editor.snapDisabled'),
          onClick: () => dispatch(setSnapMode(SnapMode.DISABLED))
        },
        {
          key: 'grid',
          label: t('editor.snapToGrid'),
          onClick: () => dispatch(setSnapMode(SnapMode.GRID))
        },
        {
          key: 'vertex',
          label: t('editor.snapToVertex'),
          onClick: () => dispatch(setSnapMode(SnapMode.VERTEX))
        }
      ]
    }
  ];
  
  // 帮助菜单
  const helpMenuItems = [
    {
      key: 'documentation',
      label: t('editor.documentation')
    },
    {
      key: 'tutorials',
      label: t('editor.tutorials')
    },
    {
      key: 'about',
      label: t('editor.about')
    }
  ];
  
  return (
    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '0 16px' }}>
      <div>
        <Space size="small">
          <Dropdown menu={{ items: fileMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.file')}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: editMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.edit')}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: viewMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.view')}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: toolsMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.tools')}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: helpMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.help')}
            </Button>
          </Dropdown>
        </Space>
      </div>
      <div>
        <Space size="small">
          <Tooltip title={t('editor.undo')}>
            <Button type="text" icon={<UndoOutlined />} style={{ color: '#fff' }} onClick={() => dispatch(undo())} />
          </Tooltip>
          <Tooltip title={t('editor.redo')}>
            <Button type="text" icon={<RedoOutlined />} style={{ color: '#fff' }} onClick={() => dispatch(redo())} />
          </Tooltip>
          <Divider type="vertical" style={{ backgroundColor: '#444', height: 20, margin: '0 8px' }} />
          <Tooltip title={isPlaying ? t('editor.pause') : t('editor.play')}>
            <Button
              type="text"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              style={{ color: '#fff' }}
              onClick={() => dispatch(setIsPlaying(!isPlaying))}
            />
          </Tooltip>
        </Space>
      </div>
    </div>
  );
};

export default Toolbar;
